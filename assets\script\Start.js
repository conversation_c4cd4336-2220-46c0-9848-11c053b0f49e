
cc.Class({
    extends: cc.Component,

    properties: {
        startbutton:cc.Node,
    },

    // LIFE-CYCLE CALLBACKS:

     onLoad () {
        this.startbutton.on('click', this.startGame, this);

     },

    start () {

    },

    // update (dt) {},

    startGame(){
        cc.director.loadScene("game");
    },
    onDestroy() {
        this.startbutton.off('click', this.startGame, this);
    }
});
